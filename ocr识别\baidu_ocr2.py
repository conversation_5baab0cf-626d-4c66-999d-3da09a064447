import requests
import base64
import os
import json
import datetime
import duckdb
import uuid

'''
增值税发票识别(支持递归子文件夹、结果保存和DuckDB存储)
'''

def init_database(db_path='ocr_results.db'):
    """初始化DuckDB数据库和表结构"""
    conn = duckdb.connect(db_path)

    # 创建版本表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS versions (
            version_id VARCHAR PRIMARY KEY,
            version_name VARCHAR NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    ''')

    # 创建OCR结果表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS ocr_results (
            id INTEGER PRIMARY KEY,
            version_id VARCHAR NOT NULL,
            file_name VARCHAR NOT NULL,
            file_path VARCHAR NOT NULL,
            json_path VARCHAR,
            scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ocr_data JSON,
            FOREIGN KEY (version_id) REFERENCES versions(version_id)
        )
    ''')

    return conn

def create_version(conn, version_name, description=None):
    """创建新版本"""
    version_id = str(uuid.uuid4())
    conn.execute('''
        INSERT INTO versions (version_id, version_name, description)
        VALUES (?, ?, ?)
    ''', [version_id, version_name, description])
    return version_id

def save_to_duckdb(conn, version_id, results):
    """保存OCR结果到DuckDB"""
    for result in results:
        conn.execute('''
            INSERT INTO ocr_results (version_id, file_name, file_path, json_path, ocr_data)
            VALUES (?, ?, ?, ?, ?)
        ''', [
            version_id,
            result['file_name'],
            result['file_path'],
            result['json_path'],
            json.dumps(result['ocr_result'], ensure_ascii=False)
        ])
    conn.commit()

def get_versions(conn):
    """获取所有版本"""
    return conn.execute('SELECT * FROM versions ORDER BY created_at DESC').fetchall()

def get_results_by_version(conn, version_id):
    """根据版本ID获取OCR结果"""
    return conn.execute('''
        SELECT * FROM ocr_results WHERE version_id = ? ORDER BY scan_time
    ''', [version_id]).fetchall()

def process_image(file_path):
    request_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice"
    with open(file_path, 'rb') as f:
        img = base64.b64encode(f.read())
    
    params = {"image": img}
    access_token = '24.60ba3e59e40ba3e026ffc9eec20f4b69.2592000.1750991415.282335-23777987'
    request_url = request_url + "?access_token=" + access_token
    headers = {'content-type': 'application/x-www-form-urlencoded'}
    response = requests.post(request_url, data=params, headers=headers)
    
    if response:
        result = response.json()
        # 保存为同名JSON文件
        json_path = os.path.splitext(file_path)[0] + '.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return {
            "file_path": file_path,
            "result": result
        }
    return None

def scan_directory(directory, version_name=None, save_to_db=True, db_path='ocr_results.db'):
    """
    扫描目录进行OCR识别

    Args:
        directory: 要扫描的目录路径
        version_name: 版本名称，如果为None则使用时间戳
        save_to_db: 是否保存到数据库
        db_path: 数据库文件路径
    """
    all_results = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(root, file)
                result = process_image(file_path)
                if result:
                    all_results.append({
                        "file_name": file,
                        "file_path": file_path,
                        "json_path": os.path.splitext(file_path)[0] + '.json',
                        "ocr_result": result['result']
                    })

    # 保存汇总结果到JSON文件
    summary_path = os.path.join(directory, 'ocr_summary.json')
    summary_data = {
        "scan_time": datetime.datetime.now().isoformat(),
        "total_files": len(all_results),
        "results": all_results
    }

    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)

    # 保存到DuckDB数据库
    if save_to_db and all_results:
        conn = init_database(db_path)

        # 如果没有指定版本名称，使用时间戳
        if version_name is None:
            version_name = f"扫描_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 创建新版本
        version_id = create_version(conn, version_name, f"扫描目录: {directory}")

        # 保存结果到数据库
        save_to_duckdb(conn, version_id, all_results)

        conn.close()

        print(f"已保存到数据库，版本: {version_name} (ID: {version_id})")

    return all_results

def list_versions(db_path='ocr_results.db'):
    """列出所有版本"""
    conn = init_database(db_path)
    versions = get_versions(conn)
    conn.close()

    print("所有版本:")
    print("-" * 80)
    for version in versions:
        print(f"版本ID: {version[0]}")
        print(f"版本名称: {version[1]}")
        print(f"创建时间: {version[2]}")
        print(f"描述: {version[3] or '无'}")
        print("-" * 80)

    return versions

def export_version_to_json(version_id, output_path, db_path='ocr_results.db'):
    """导出指定版本的数据到JSON文件"""
    conn = init_database(db_path)
    results = get_results_by_version(conn, version_id)
    conn.close()

    export_data = []
    for result in results:
        export_data.append({
            "id": result[0],
            "file_name": result[2],
            "file_path": result[3],
            "json_path": result[4],
            "scan_time": result[5],
            "ocr_data": json.loads(result[6])
        })

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump({
            "version_id": version_id,
            "export_time": datetime.datetime.now().isoformat(),
            "total_records": len(export_data),
            "results": export_data
        }, f, ensure_ascii=False, indent=2)

    print(f"已导出版本 {version_id} 的数据到 {output_path}")

def delete_version(version_id, db_path='ocr_results.db'):
    """删除指定版本的数据"""
    conn = init_database(db_path)

    # 删除OCR结果
    conn.execute('DELETE FROM ocr_results WHERE version_id = ?', [version_id])

    # 删除版本记录
    conn.execute('DELETE FROM versions WHERE version_id = ?', [version_id])

    conn.commit()
    conn.close()

    print(f"已删除版本 {version_id}")

# 使用示例
if __name__ == "__main__":
    # 示例1: 基本使用 - 扫描目录并保存到数据库
    # scan_directory(r'I:\环渤海战区\快递', version_name="快递发票扫描_v1")

    # 示例2: 只保存到JSON文件，不保存到数据库
    # scan_directory(r'I:\环渤海战区\快递', save_to_db=False)

    # 示例3: 列出所有版本
    # list_versions()

    # 示例4: 导出指定版本到JSON文件
    # export_version_to_json("your-version-id", "exported_data.json")

    # 示例5: 删除指定版本
    # delete_version("your-version-id")

    print("OCR工具已准备就绪！")
    print("请取消注释相应的示例代码来使用功能。")