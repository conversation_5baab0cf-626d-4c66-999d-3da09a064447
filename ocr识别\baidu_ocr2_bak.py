import requests
import base64
import os
import json
import datetime

'''
增值税发票识别(支持递归子文件夹和结果保存)
'''

def process_image(file_path):
    request_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice"
    with open(file_path, 'rb') as f:
        img = base64.b64encode(f.read())
    
    params = {"image": img}
    access_token = '24.60ba3e59e40ba3e026ffc9eec20f4b69.2592000.1750991415.282335-23777987'
    request_url = request_url + "?access_token=" + access_token
    headers = {'content-type': 'application/x-www-form-urlencoded'}
    response = requests.post(request_url, data=params, headers=headers)
    
    if response:
        result = response.json()
        # 保存为同名JSON文件
        json_path = os.path.splitext(file_path)[0] + '.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return {
            "file_path": file_path,
            "result": result
        }
    return None

def scan_directory(directory):
    all_results = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(root, file)
                result = process_image(file_path)
                if result:
                    all_results.append({
                        "file_name": file,
                        "file_path": file_path,
                        "json_path": os.path.splitext(file_path)[0] + '.json',
                        "ocr_result": result['result']
                    })
    
    # 保存汇总结果
    summary_path = os.path.join(directory, 'ocr_summary.json')
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump({
            "scan_time": datetime.datetime.now().isoformat(),
            "total_files": len(all_results),
            "results": all_results
        }, f, ensure_ascii=False, indent=2)
    
    return all_results

# 使用示例 - 替换为您的实际目录路径
scan_directory(r'I:\环渤海战区\快递')
