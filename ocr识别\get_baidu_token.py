#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import datetime

def get_access_token(api_key, secret_key):
    """获取百度OCR API的access_token"""
    url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={api_key}&client_secret={secret_key}"
    
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    return None

if __name__ == "__main__":
    # 使用提供的API密钥
    API_KEY = "fqXxXEvFsrjb8xfR4EXh9RXa"
    SECRET_KEY = "pVHqDWabWzna1G0GNQ8eh0Ho8k90XDZk"
    
    print("正在获取百度OCR API的access_token...")
    
    # 获取token
    result = get_access_token(API_KEY, SECRET_KEY)
    
    if result and "access_token" in result:
        token = result["access_token"]
        expires = result["expires_in"]
        
        # 计算过期时间
        now = datetime.datetime.now()
        expire_date = now + datetime.timedelta(seconds=expires)
        
        # 保存token到文件
        with open("baidu_ocr_token.txt", "w") as f:
            f.write(token)
        
        # 保存详细信息到JSON文件
        with open("baidu_ocr_token_info.json", "w", encoding="utf-8") as f:
            result["request_time"] = now.strftime("%Y-%m-%d %H:%M:%S")
            result["expire_time"] = expire_date.strftime("%Y-%m-%d %H:%M:%S")
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"Access Token: {token}")
        print(f"有效期: {expires} 秒 (约 {expires/86400:.1f} 天)")
        print(f"过期时间: {expire_date.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Token已保存到 baidu_ocr_token.txt")
        print(f"详细信息已保存到 baidu_ocr_token_info.json")
    else:
        print("获取token失败！")
        print(result)