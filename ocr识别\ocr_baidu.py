import requests
import base64
import json
'''
增值税发票识别
'''

request_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice"
# 二进制方式打开图片文件
f = open(r"\\*************\部门资料库\设计部\设计部资料共享\新建文件夹\非核验发票\24442000000022483369#24442000000022483369.jpg", 'rb')
img = base64.b64encode(f.read())

params = {"image":img}
access_token = '24.ee5f56e8efcc69b3f5d1ee3e9338b6c7.2592000.1755054509.282335-23777987'
request_url = request_url + "?access_token=" + access_token
headers = {'content-type': 'application/x-www-form-urlencoded'}
response = requests.post(request_url, data=params, headers=headers)
if response:
    result= response.json()
    print (result)
    with open('./demo.json', 'w',encoding='utf-8') as f:
        json.dump(result,f,ensure_ascii=False,indent=2)

f.close()