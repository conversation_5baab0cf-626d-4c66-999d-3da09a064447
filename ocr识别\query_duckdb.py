import duckdb

def query_invoice_summary(db_path='invoice_data.db'):
    """查询发票摘要信息：发票代码、发票号码、开票日期、货物金额和购买方名称"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称"
        FROM 
            invoices i
        ORDER BY 
            i.scan_time DESC
    ''').fetchall()
    
    conn.close()
    
    # 打印结果
    print("\n发票摘要信息:")
    print("-" * 100)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30}")
    print("-" * 100)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30}")
    
    print("-" * 100)
    print(f"共 {len(results)} 条记录")
    
    return results

def query_invoice_items(db_path='invoice_data.db'):
    """查询发票明细信息，包括每个发票的商品明细"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.purchaser_name AS "购买方名称",
            it.name AS "商品名称",
            it.specification AS "规格型号",
            it.quantity AS "数量",
            it.unit AS "单位",
            it.price AS "单价",
            it.amount AS "金额(元)",
            it.tax_rate AS "税率",
            it.tax AS "税额(元)"
        FROM 
            invoices i
        JOIN 
            invoice_items it ON i.id = it.invoice_id
        ORDER BY 
            i.scan_time DESC, it.row_num
    ''').fetchall()
    
    conn.close()
    
    # 打印结果
    print("\n发票商品明细信息:")
    print("-" * 120)
    print(f"{'发票代码':<12} {'发票号码':<12} {'开票日期':<10} {'购买方名称':<20} {'商品名称':<15} {'金额(元)':<10}")
    print("-" * 120)
    
    current_invoice = None
    total_items = 0
    
    for row in results:
        invoice_id = f"{row[0]}-{row[1]}"
        if current_invoice != invoice_id:
            current_invoice = invoice_id
            print(f"\n{row[0]:<12} {row[1]:<12} {row[2]:<10} {row[3]:<20}")
            print(f"  {'商品名称':<15} {'规格型号':<15} {'数量':<8} {'单位':<5} {'单价':<12} {'金额(元)':<10} {'税率':<6} {'税额(元)':<10}")
            print(f"  {'-'*85}")
        
        print(f"  {row[4]:<15} {row[5]:<15} {row[6]:<8.2f} {row[7]:<5} {row[8]:<12.6f} {row[9]:<10.2f} {row[10]:<6} {row[11]:<10.2f}")
        total_items += 1
    
    print("-" * 120)
    print(f"共 {len(results)} 条商品记录，涉及 {len(set([f'{r[0]}-{r[1]}' for r in results]))} 张发票")
    
    return results

def query_invoice_with_aggregated_items(db_path='invoice_data.db'):
    """查询发票信息，并聚合相同发票号码的商品名称"""
    conn = duckdb.connect(db_path)
    
    # 执行查询，使用STRING_AGG函数聚合商品名称
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称",
            STRING_AGG(it.name, CHR(10)) AS "商品名称列表"
        FROM 
            invoices i
        LEFT JOIN 
            invoice_items it ON i.id = it.invoice_id
        GROUP BY 
            i.invoice_code, i.invoice_num, i.invoice_date, i.amount_in_figuers, i.purchaser_name
        ORDER BY 
            i.scan_time DESC
    ''').fetchall()
    
    conn.close()
    
    # 打印结果
    print("\n发票信息(含聚合商品名称):")
    print("-" * 120)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30} {'商品名称列表'}")
    print("-" * 120)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30}")
        # 打印商品名称列表，每行前面加两个空格
        if row[5]:
            for item_name in row[5].split('\n'):
                print(f"  {item_name}")
        print("-" * 80)
    
    print("-" * 120)
    print(f"共 {len(results)} 条记录")
    
    return results

# 添加一个简单的查询菜单函数
def query_menu():
    """查询菜单"""
    while True:
        print("\n" + "="*50)
        print("发票数据查询")
        print("="*50)
        print("1. 查询发票摘要信息")
        print("2. 查询发票商品明细")
        print("3. 按购买方名称查询")
        print("4. 按日期范围查询")
        print("5. 按金额范围查询")
        print("6. 查询发票(含聚合商品名称)")
        print("0. 返回主菜单")
        
        choice = input("\n请选择查询类型 (0-6): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            query_invoice_summary()
        elif choice == '2':
            query_invoice_items()
        elif choice == '3':
            name = input("请输入购买方名称关键字: ")
            query_by_purchaser_name(name)
        elif choice == '4':
            start_date = input("请输入开始日期 (格式: YYYYMMDD): ")
            end_date = input("请输入结束日期 (格式: YYYYMMDD): ")
            query_by_date_range(start_date, end_date)
        elif choice == '5':
            min_amount = input("请输入最小金额: ")
            max_amount = input("请输入最大金额: ")
            query_by_amount_range(float(min_amount) if min_amount else 0, 
                                 float(max_amount) if max_amount else float('inf'))
        elif choice == '6':
            query_invoice_with_aggregated_items()
        else:
            print("无效选择，请重试")

def query_by_purchaser_name(name, db_path='invoice_data.db'):
    """按购买方名称查询发票"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称",
            i.seller_name AS "销售方名称"
        FROM 
            invoices i
        WHERE 
            i.purchaser_name LIKE ?
        ORDER BY 
            i.scan_time DESC
    ''', ['%' + name + '%']).fetchall()
    
    conn.close()
    
    # 打印结果
    print(f"\n按购买方名称查询 - 关键字: '{name}'")
    print("-" * 120)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30} {'销售方名称':<30}")
    print("-" * 120)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30} {row[5]:<30}")
    
    print("-" * 120)
    print(f"共 {len(results)} 条记录")
    
    return results

def query_by_date_range(start_date, end_date, db_path='invoice_data.db'):
    """按日期范围查询发票"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称"
        FROM 
            invoices i
        WHERE 
            i.invoice_date >= ? AND i.invoice_date <= ?
        ORDER BY 
            i.invoice_date
    ''', [start_date, end_date]).fetchall()
    
    conn.close()
    
    # 打印结果
    print(f"\n按日期范围查询 - 从 {start_date} 到 {end_date}")
    print("-" * 100)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30}")
    print("-" * 100)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30}")
    
    print("-" * 100)
    print(f"共 {len(results)} 条记录")
    
    return results

def query_by_amount_range(min_amount, max_amount, db_path='invoice_data.db'):
    """按金额范围查询发票"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称"
        FROM 
            invoices i
        WHERE 
            i.amount_in_figuers >= ? AND i.amount_in_figuers <= ?
        ORDER BY 
            i.amount_in_figuers DESC
    ''', [min_amount, max_amount]).fetchall()
    
    conn.close()
    
    # 打印结果
    print(f"\n按金额范围查询 - 从 {min_amount} 到 {max_amount} 元")
    print("-" * 100)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30}")
    print("-" * 100)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30}")
    
    print("-" * 100)
    print(f"共 {len(results)} 条记录")
    
    return results

if __name__== "__main__":
    query_menu()
