import requests
import base64
import os
import json
import datetime
import duckdb
import uuid

'''
增值税发票智能识别与DuckDB存储
- 支持智能重复检测
- 支持模糊匹配
- 支持发票合并
- 支持聚合查询
'''

def normalize_invoice_code(code):
    """标准化发票代码，只保留数字"""
    if not code:
        return ""
    return ''.join(c for c in str(code) if c.isdigit())

def normalize_invoice_num(num):
    """标准化发票号码，只保留数字"""
    if not num:
        return ""
    return ''.join(c for c in str(num) if c.isdigit())

def init_database(db_path='invoice_data.db'):
    """初始化DuckDB数据库和表结构"""
    conn = duckdb.connect(db_path)

    # 创建版本表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS versions (
            version_id VARCHAR PRIMARY KEY,
            version_name VARCHAR NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    ''')
    
    # 创建序列用于自增ID
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_invoices_id START 1;")
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_items_id START 1;")

    # 创建发票信息表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY DEFAULT nextval('seq_invoices_id'),
            version_id VARCHAR NOT NULL,
            file_name VARCHAR NOT NULL,
            file_path VARCHAR NOT NULL,
            
            -- 发票基本信息
            invoice_code VARCHAR,           -- 发票代码
            invoice_num VARCHAR,            -- 发票号码
            normalized_code VARCHAR,        -- 标准化发票代码（仅数字）
            normalized_num VARCHAR,         -- 标准化发票号码（仅数字）
            invoice_date VARCHAR,           -- 开票日期
            invoice_type VARCHAR,           -- 发票类型
            
            -- 购销方信息
            purchaser_name VARCHAR,         -- 购买方名称
            purchaser_register_num VARCHAR, -- 购买方税号
            seller_name VARCHAR,            -- 销售方名称
            seller_register_num VARCHAR,    -- 销售方税号
            
            -- 金额信息
            total_amount DECIMAL(12,2),     -- 合计金额
            total_tax DECIMAL(12,2),        -- 合计税额
            amount_in_figuers DECIMAL(12,2),-- 价税合计(小写)
            amount_in_words VARCHAR,        -- 价税合计(大写)
            
            -- 其他信息
            remarks VARCHAR,                -- 备注
            
            -- 原始数据
            json_data JSON,                 -- 完整JSON数据
            
            scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (version_id) REFERENCES versions(version_id)
        )
    ''')

    # 创建商品明细表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY DEFAULT nextval('seq_items_id'),
            invoice_id INTEGER NOT NULL,
            row_num INTEGER,
            
            -- 商品信息
            name VARCHAR,                   -- 商品名称
            specification VARCHAR,          -- 规格型号
            unit VARCHAR,                   -- 单位
            quantity DECIMAL(12,2),         -- 数量
            price DECIMAL(31,13),           -- 单价
            amount DECIMAL(12,2),           -- 金额
            tax_rate VARCHAR,               -- 税率
            tax DECIMAL(12,2),              -- 税额
            
            FOREIGN KEY (invoice_id) REFERENCES invoices(id)
        )
    ''')

    return conn

def create_version(conn, version_name, description=None):
    """创建新版本"""
    version_id = str(uuid.uuid4())
    conn.execute('''
        INSERT INTO versions (version_id, version_name, description)
        VALUES (?, ?, ?)
    ''', [version_id, version_name, description])
    return version_id

def extract_invoice_data(ocr_result, file_name, file_path):
    """从OCR结果中提取发票数据"""
    words = ocr_result.get("words_result", {})
    
    # 提取基本信息
    invoice_code = words.get("InvoiceCode", {}).get("word", "")
    invoice_num = words.get("InvoiceNum", {}).get("word", "")
    invoice_date = words.get("InvoiceDate", {}).get("word", "")
    invoice_type = words.get("InvoiceType", {}).get("word", "")
    
    # 提取购销方信息
    purchaser_name = words.get("PurchaserName", {}).get("word", "")
    purchaser_register_num = words.get("PurchaserRegisterNum", {}).get("word", "")
    seller_name = words.get("SellerName", {}).get("word", "")
    seller_register_num = words.get("SellerRegisterNum", {}).get("word", "")
    
    # 提取金额信息
    total_amount = words.get("TotalAmount", {}).get("word", "0")
    total_tax = words.get("TotalTax", {}).get("word", "0")
    amount_in_figuers = words.get("AmountInFiguers", {}).get("word", "0")
    amount_in_words = words.get("AmountInWords", {}).get("word", "")
    
    # 提取备注
    remarks = words.get("Remarks", {}).get("word", "")
    
    # 标准化金额
    try:
        total_amount = float(total_amount.replace(",", ""))
    except:
        total_amount = 0
        
    try:
        total_tax = float(total_tax.replace(",", ""))
    except:
        total_tax = 0
        
    try:
        amount_in_figuers = float(amount_in_figuers.replace(",", ""))
    except:
        amount_in_figuers = 0
    
    # 提取商品明细
    commodity_info = words.get("CommodityInfo", [])
    items = []
    
    for i, item in enumerate(commodity_info):
        item_data = {
            "row_num": i + 1,
            "name": item.get("CommodityName", ""),
            "specification": item.get("CommodityType", ""),
            "unit": item.get("CommodityUnit", ""),
            "quantity": 0,
            "price": 0,
            "amount": 0,
            "tax_rate": item.get("CommodityTaxRate", ""),
            "tax": 0
        }
        
        # 转换数值
        try:
            item_data["quantity"] = float(item.get("CommodityNum", "0").replace(",", ""))
        except:
            pass
            
        try:
            item_data["price"] = float(item.get("CommodityPrice", "0").replace(",", ""))
        except:
            pass
            
        try:
            item_data["amount"] = float(item.get("CommodityAmount", "0").replace(",", ""))
        except:
            pass
            
        try:
            item_data["tax"] = float(item.get("CommodityTax", "0").replace(",", ""))
        except:
            pass
        
        items.append(item_data)
    
    # 创建发票数据字典
    invoice_data = {
        "file_name": file_name,
        "file_path": file_path,
        "invoice_code": invoice_code,
        "invoice_num": invoice_num,
        "normalized_code": normalize_invoice_code(invoice_code),
        "normalized_num": normalize_invoice_num(invoice_num),
        "invoice_date": invoice_date,
        "invoice_type": invoice_type,
        "purchaser_name": purchaser_name,
        "purchaser_register_num": purchaser_register_num,
        "seller_name": seller_name,
        "seller_register_num": seller_register_num,
        "total_amount": total_amount,
        "total_tax": total_tax,
        "amount_in_figuers": amount_in_figuers,
        "amount_in_words": amount_in_words,
        "remarks": remarks,
        "json_data": json.dumps(ocr_result, ensure_ascii=False)
    }
    
    return invoice_data, items

def check_invoice_exists(conn, invoice_code, invoice_num, seller_name=None, purchaser_name=None, invoice_date=None, fuzzy_match=True):
    """
    检查发票是否已存在于数据库中，支持模糊匹配
    
    参数:
    - conn: 数据库连接
    - invoice_code: 发票代码
    - invoice_num: 发票号码
    - seller_name: 销售方名称（可选，用于增强匹配）
    - purchaser_name: 购买方名称（可选，用于增强匹配）
    - invoice_date: 开票日期（可选，用于增强匹配）
    - fuzzy_match: 是否使用模糊匹配（默认为True）
    
    返回:
    - 如果发票存在，返回发票ID；否则返回None
    """
    # 标准化发票代码和号码
    norm_code = normalize_invoice_code(invoice_code)
    norm_num = normalize_invoice_num(invoice_num)
    
    if not norm_code or not norm_num:
        return None  # 如果代码或号码为空，无法判断重复
    
    if fuzzy_match:
        # 模糊匹配：使用标准化字段和额外条件
        query = '''
            SELECT id FROM invoices 
            WHERE 
                (normalized_code = ? OR invoice_code = ?) 
                AND (normalized_num = ? OR invoice_num = ?)
        '''
        params = [norm_code, invoice_code, norm_num, invoice_num]
        
        # 添加额外的匹配条件
        if seller_name:
            query += " AND seller_name = ?"
            params.append(seller_name)
        if purchaser_name:
            query += " AND purchaser_name = ?"
            params.append(purchaser_name)
        if invoice_date:
            query += " AND invoice_date = ?"
            params.append(invoice_date)
    else:
        # 精确匹配：只匹配原始代码和号码
        query = '''
            SELECT id FROM invoices 
            WHERE 
                invoice_code = ? AND invoice_num = ?
        '''
        params = [invoice_code, invoice_num]
    
    result = conn.execute(query, params).fetchone()
    return result[0] if result else None

def find_similar_invoices(conn, invoice_data, threshold=0.8):
    """
    查找相似的发票
    
    参数:
    - conn: 数据库连接
    - invoice_data: 发票数据字典
    - threshold: 相似度阈值（0-1之间）
    
    返回:
    - 相似发票的列表
    """
    # 获取所有发票
    all_invoices = conn.execute('''
        SELECT 
            id, invoice_code, invoice_num, normalized_code, normalized_num,
            invoice_date, purchaser_name, seller_name, amount_in_figuers
        FROM invoices
    ''').fetchall()
    
    similar_invoices = []
    
    # 标准化当前发票数据
    norm_code = normalize_invoice_code(invoice_data.get("invoice_code", ""))
    norm_num = normalize_invoice_num(invoice_data.get("invoice_num", ""))
    
    for inv in all_invoices:
        # 计算相似度分数
        score = 0
        total_weight = 0
        
        # 发票代码匹配（权重：0.3）
        db_norm_code = inv[3] if inv[3] else normalize_invoice_code(inv[1])
        if norm_code and db_norm_code:
            if norm_code == db_norm_code:
                score += 0.3
            elif norm_code in db_norm_code or db_norm_code in norm_code:
                score += 0.2
        total_weight += 0.3
        
        # 发票号码匹配（权重：0.3）
        db_norm_num = inv[4] if inv[4] else normalize_invoice_num(inv[2])
        if norm_num and db_norm_num:
            if norm_num == db_norm_num:
                score += 0.3
            elif norm_num in db_norm_num or db_norm_num in norm_num:
                score += 0.2
        total_weight += 0.3
        
        # 开票日期匹配（权重：0.1）
        if invoice_data.get("invoice_date") and inv[5] and invoice_data["invoice_date"] == inv[5]:
            score += 0.1
        total_weight += 0.1
        
        # 购买方名称匹配（权重：0.1）
        if invoice_data.get("purchaser_name") and inv[6]:
            if invoice_data["purchaser_name"] == inv[6]:
                score += 0.1
            elif invoice_data["purchaser_name"] in inv[6] or inv[6] in invoice_data["purchaser_name"]:
                score += 0.05
        total_weight += 0.1
        
        # 销售方名称匹配（权重：0.1）
        if invoice_data.get("seller_name") and inv[7]:
            if invoice_data["seller_name"] == inv[7]:
                score += 0.1
            elif invoice_data["seller_name"] in inv[7] or inv[7] in invoice_data["seller_name"]:
                score += 0.05
        total_weight += 0.1
        
        # 金额匹配（权重：0.1）
        if invoice_data.get("amount_in_figuers") and inv[8]:
            try:
                current_amount = float(invoice_data["amount_in_figuers"])
                db_amount = float(inv[8])
                # 如果金额差异小于1%，认为匹配
                if abs(current_amount - db_amount) / max(current_amount, db_amount) < 0.01:
                    score += 0.1
            except (ValueError, TypeError):
                pass
        total_weight += 0.1
        
        # 计算最终相似度
        if total_weight > 0:
            final_score = score / total_weight
            if final_score >= threshold:
                similar_invoices.append({
                    "id": inv[0],
                    "invoice_code": inv[1],
                    "invoice_num": inv[2],
                    "normalized_code": inv[3],
                    "normalized_num": inv[4],
                    "invoice_date": inv[5],
                    "purchaser_name": inv[6],
                    "seller_name": inv[7],
                    "amount_in_figuers": inv[8],
                    "similarity": final_score
                })
    
    # 按相似度降序排序
    similar_invoices.sort(key=lambda x: x["similarity"], reverse=True)
    return similar_invoices

def save_to_duckdb(conn, version_id, invoice_data, items):
    """保存发票数据到DuckDB"""
    # 插入发票主表
    conn.execute('''
        INSERT INTO invoices (
            version_id, file_name, file_path, invoice_code, invoice_num, 
            normalized_code, normalized_num, invoice_date, invoice_type, 
            purchaser_name, purchaser_register_num, seller_name, seller_register_num, 
            total_amount, total_tax, amount_in_figuers, amount_in_words, 
            remarks, json_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', [
        version_id, invoice_data["file_name"], invoice_data["file_path"],
        invoice_data["invoice_code"], invoice_data["invoice_num"],
        invoice_data["normalized_code"], invoice_data["normalized_num"],
        invoice_data["invoice_date"], invoice_data["invoice_type"],
        invoice_data["purchaser_name"], invoice_data["purchaser_register_num"],
        invoice_data["seller_name"], invoice_data["seller_register_num"],
        invoice_data["total_amount"], invoice_data["total_tax"],
        invoice_data["amount_in_figuers"], invoice_data["amount_in_words"],
        invoice_data["remarks"], invoice_data["json_data"]
    ])
    
    # 获取插入的发票ID
    invoice_id = conn.execute("SELECT currval('seq_invoices_id')").fetchone()[0]
    
    # 插入商品明细
    for item in items:
        conn.execute('''
            INSERT INTO invoice_items (
                invoice_id, row_num, name, specification, unit, 
                quantity, price, amount, tax_rate, tax
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
            invoice_id, item["row_num"], item["name"], item["specification"],
            item["unit"], item["quantity"], item["price"], item["amount"],
            item["tax_rate"], item["tax"]
        ])
    
    conn.commit()
    return invoice_id

def save_to_duckdb_with_duplicate_check(conn, version_id, invoice_data, items, fuzzy_match=True, similarity_threshold=0.8):
    """保存发票数据到DuckDB，检查重复（支持模糊匹配）"""
    # 检查发票是否已存在
    existing_id = check_invoice_exists(
        conn, 
        invoice_data["invoice_code"], 
        invoice_data["invoice_num"],
        invoice_data["seller_name"],
        invoice_data["purchaser_name"],
        invoice_data["invoice_date"],
        fuzzy_match
    )
    
    if existing_id:
        print(f"发票已存在: {invoice_data['invoice_code']}-{invoice_data['invoice_num']}, ID: {existing_id}")
        return existing_id
    
    # 如果没有找到精确匹配，查找相似发票
    if fuzzy_match:
        similar_invoices = find_similar_invoices(conn, invoice_data, similarity_threshold)
        if similar_invoices:
            print(f"找到 {len(similar_invoices)} 个相似发票:")
            for i, inv in enumerate(similar_invoices[:3]):  # 只显示前3个
                print(f"  {i+1}. ID: {inv['id']}, 代码: {inv['invoice_code']}, 号码: {inv['invoice_num']}, 相似度: {inv['similarity']:.2f}")
            
            # 可以在这里添加交互式确认，或者自动使用最相似的发票
            if similar_invoices[0]["similarity"] > 0.9:  # 如果相似度非常高
                print(f"自动使用最相似的发票 ID: {similar_invoices[0]['id']}")
                return similar_invoices[0]["id"]
    
    # 如果没有找到匹配或相似发票，正常插入
    return save_to_duckdb(conn, version_id, invoice_data, items)

def process_image(file_path):
    """处理图片并进行OCR识别"""
    request_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice"
    with open(file_path, 'rb') as f:
        img = base64.b64encode(f.read())
    
    params = {"image": img}
    access_token = '24.ee5f56e8efcc69b3f5d1ee3e9338b6c7.2592000.1755054509.282335-23777987'
    request_url = request_url + "?access_token=" + access_token
    headers = {'content-type': 'application/x-www-form-urlencoded'}
    response = requests.post(request_url, data=params, headers=headers)
    
    if response:
        result = response.json()
        # 保存为同名JSON文件
        json_path = os.path.splitext(file_path)[0] + '.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return result
    return None

def scan_directory_with_smart_duplicate_check(directory, version_name=None, save_to_db=True, db_path='invoice_data.db', fuzzy_match=True):
    """扫描目录进行OCR识别并保存到DuckDB，使用智能重复检查"""
    all_results = []
    duplicates = 0
    new_invoices = 0
    
    # 如果没有指定版本名称，使用时间戳
    if version_name is None:
        version_name = f"扫描_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 初始化数据库连接
    conn = init_database(db_path) if save_to_db else None
    
    # 创建新版本
    version_id = create_version(conn, version_name, f"扫描目录: {directory}") if save_to_db else None
    
    # 扫描目录
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(root, file)
                print(f"处理文件: {file_path}")
                
                # OCR识别
                ocr_result = process_image(file_path)
                
                if ocr_result:
                    # 提取发票数据
                    invoice_data, items = extract_invoice_data(ocr_result, file, file_path)
                    
                    # 保存到数据库，智能检查重复
                    if save_to_db:
                        existing_id = check_invoice_exists(
                            conn, 
                            invoice_data["invoice_code"], 
                            invoice_data["invoice_num"],
                            invoice_data["seller_name"],
                            invoice_data["purchaser_name"],
                            invoice_data["invoice_date"],
                            fuzzy_match
                        )
                        
                        if existing_id:
                            print(f"  发票已存在，跳过: {invoice_data['invoice_code']}-{invoice_data['invoice_num']}, ID: {existing_id}")
                            duplicates += 1
                        else:
                            # 查找相似发票
                            if fuzzy_match:
                                similar_invoices = find_similar_invoices(conn, invoice_data)
                                if similar_invoices:
                                    print(f"  找到 {len(similar_invoices)} 个相似发票:")
                                    for i, inv in enumerate(similar_invoices[:3]):  # 只显示前3个
                                        print(f"    {i+1}. ID: {inv['id']}, 代码: {inv['invoice_code']}, 号码: {inv['invoice_num']}, 相似度: {inv['similarity']:.2f}")
                                    
                                    # 如果相似度非常高，认为是重复
                                    if similar_invoices[0]["similarity"] > 0.9:
                                        print(f"  自动识别为重复发票，跳过: {invoice_data['invoice_code']}-{invoice_data['invoice_num']}")
                                        duplicates += 1
                                        continue
                            
                            # 插入新发票
                            invoice_id = save_to_duckdb(conn, version_id, invoice_data, items)
                            print(f"  已保存到数据库，发票ID: {invoice_id}")
                            new_invoices += 1
                    
                    all_results.append({
                        "file_name": file,
                        "file_path": file_path,
                        "invoice_data": invoice_data,
                        "items": items
                    })
    
    # 关闭数据库连接
    if conn:
        conn.close()
    
    print(f"扫描完成，共处理 {len(all_results)} 个文件，新增 {new_invoices} 张发票，跳过 {duplicates} 个重复发票")
    return all_results

def get_versions(db_path='invoice_data.db'):
    """获取所有版本"""
    conn = duckdb.connect(db_path)
    versions = conn.execute('SELECT * FROM versions ORDER BY created_at DESC').fetchall()
    conn.close()
    return versions

def get_invoices_by_version(version_id, db_path='invoice_data.db'):
    """获取指定版本的所有发票"""
    conn = duckdb.connect(db_path)
    invoices = conn.execute('''
        SELECT * FROM invoices WHERE version_id = ? ORDER BY scan_time
    ''', [version_id]).fetchall()
    conn.close()
    return invoices

def get_invoice_items(invoice_id, db_path='invoice_data.db'):
    """获取发票的商品明细"""
    conn = duckdb.connect(db_path)
    items = conn.execute('''
        SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY row_num
    ''', [invoice_id]).fetchall()
    conn.close()
    return items

def query_invoice_summary(db_path='invoice_data.db'):
    """查询发票摘要信息"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.amount_in_figuers AS "价税合计(元)",
            i.purchaser_name AS "购买方名称"
        FROM 
            invoices i
        ORDER BY 
            i.scan_time DESC
    ''').fetchall()
    
    conn.close()
    
    # 打印结果
    print("\n发票摘要信息:")
    print("-" * 100)
    print(f"{'发票代码':<15} {'发票号码':<15} {'开票日期':<12} {'价税合计(元)':<15} {'购买方名称':<30}")
    print("-" * 100)
    
    for row in results:
        print(f"{row[0]:<15} {row[1]:<15} {row[2]:<12} {row[3]:<15.2f} {row[4]:<30}")
    
    print("-" * 100)
    print(f"共 {len(results)} 条记录")
    
    return results

def query_invoice_items(db_path='invoice_data.db'):
    """查询发票明细信息，包括每个发票的商品明细"""
    conn = duckdb.connect(db_path)
    
    # 执行查询
    results = conn.execute('''
        SELECT 
            i.invoice_code AS "发票代码",
            i.invoice_num AS "发票号码",
            i.invoice_date AS "开票日期",
            i.purchaser_name AS "购买方名称",
            it.name AS "商品名称",
            it.specification AS "规格型号",
            it.quantity AS "数量",
            it.unit AS "单位",
            it.price AS "单价",
            it.amount AS "金额(元)",
            it.tax_rate AS "税率",
            it.tax AS "税额(元)"
        FROM 
            invoices i
        JOIN 
            invoice_items it ON i.id = it.invoice_id
        ORDER BY 
            i.scan_time DESC, it.row_num
    ''').fetchall()
    
    conn.close()
    
    # 打印结果
    print("\n发票商品明细信息:")
    print("-" * 120)
    print(f"{'发票代码':<12} {'发票号码':<12} {'开票日期':<10} {'购买方名称':<20} {'商品名称':<15} {'金额(元)':<10}")
    print("-" * 120)
    
    current_invoice = None
    total_items = 0
    
    for row in results:
        invoice_id = f"{row[0]}-{row[1]}"
        if current_invoice != invoice_id:
            current_invoice = invoice_id
            print(f"\n{row[0]:<12} {row[1]:<12} {row[2]:<10} {row[3]:<20}")
            print(f"  {'商品名称':<15} {'规格型号':<15} {'数量':<8} {'单位':<5} {'单价':<12} {'金额(元)':<10} {'税率':<6} {'税额(元)':<10}")
            print(f"  {'-'*85}")
        
        print(f"  {row[4]:<15} {row[5]:<15} {row[6]:<8.2f} {row[7]:<5} {row[8]:<12.6f} {row[9]:<10.2f} {row[10]:<6} {row[11]:<10.2f}")
        total_items += 1
    
    print("-" * 120)
    print(f"共 {len(results)} 条商品记录，涉及 {len(set([f'{r[0]}-{r[1]}' for r in results]))} 张发票")
    
    return results

def query_invoice_with_aggregated_items(db_path='invoice_data.db'):
    """查询发票信息，并聚合相同发票号码的