{"words_result": {"PurchaserAddress": "", "PurchaserBank": "", "Password": "", "CommodityVehicleType": [], "SellerRegisterNum": "9144010172562200XC", "SellerBank": "", "CommodityNum": [{"row": "1", "word": "1"}, {"row": "2", "word": "2"}, {"row": "3", "word": "2"}], "CommodityAmount": [{"row": "1", "word": "2522.12"}, {"row": "2", "word": "2796.46"}, {"row": "3", "word": "2761.06"}], "InvoiceType": "电子发票(专用发票)", "AmountInWords": "玖仟壹佰叁拾圆整", "TotalTax": "1050.36", "MachineCode": "", "City": "", "InvoiceNumDigit": "", "Checker": "", "InvoiceCode": "", "SellerAddress": "", "CommodityPrice": [{"row": "1", "word": "*****************"}, {"row": "2", "word": "1398.*************"}, {"row": "3", "word": "1380.*************"}], "NoteDrawer": "陈玉霞", "Province": "", "InvoiceNum": "24442000000022483369", "CommodityTaxRate": [{"row": "1", "word": "13%"}, {"row": "2", "word": "13%"}, {"row": "3", "word": "13%"}], "ServiceType": "服务", "InvoiceDate": "2024年01月16日", "CommodityEndDate": [], "PurchaserRegisterNum": "91431100X17256184L", "CommodityStartDate": [], "TotalAmount": "8079.64", "SheetNum": "", "CommodityPlateNum": [], "PurchaserName": "中国农业银行股份有限公司永州分行", "SellerName": "广州市百利文仪实业有限公司", "InvoiceNumConfirm": "24442000000022483369", "Agent": "否", "InvoiceTag": "其他", "CommodityUnit": [{"row": "1", "word": "套"}, {"row": "2", "word": "套"}, {"row": "3", "word": "套"}], "CheckCode": "", "InvoiceTypeOrg": "电子发票(增值税专用发票)", "Remarks": "永州竹城", "Payee": "", "CommodityTax": [{"row": "1", "word": "327.88"}, {"row": "2", "word": "363.54"}, {"row": "3", "word": "358.94"}], "AmountInFiguers": "9130.00", "CommodityName": [{"row": "1", "word": "*家具*综合服务台一(柜员沙发)"}, {"row": "2", "word": "*家具*员工座椅"}, {"row": "3", "word": "*家具*集中办公区直排工位"}], "CommodityType": [{"row": "1", "word": "1200*550*800H"}, {"row": "2", "word": "常规"}, {"row": "3", "word": "1500*600*750H"}], "OnlinePay": "", "PassengerName": [], "PassengerIdNum": [], "PassengerDate": [], "PassengerDeparture": [], "PassengerArrival": [], "PassengerClass": [], "PassengerVehicleType": [], "TransportType": [], "TransportPlateNum": [], "TransportDeparture": [], "TransportArrival": [], "TransportCargoInfo": []}, "words_result_num": 61, "log_id": 1944597792744582316}