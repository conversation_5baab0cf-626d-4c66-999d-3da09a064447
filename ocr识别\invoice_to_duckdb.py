import requests
import base64
import os
import json
import datetime
import duckdb
import uuid

'''
增值税发票识别与DuckDB存储
基于demo.json的结构，保存关键发票信息
'''

def init_database(db_path='invoice_data.db'):
    """初始化DuckDB数据库和表结构"""
    conn = duckdb.connect(db_path)

    # 创建版本表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS versions (
            version_id VARCHAR PRIMARY KEY,
            version_name VARCHAR NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    ''')
    #创建自增 
    # conn.execute("CREATE SEQUENCE seq_id START 1;")
        # --- ACTION 1: Create a sequence for the 'invoices' table ---
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_invoices_id START 1;")

    # --- ACTION 2: Create a separate sequence for the 'invoice_items' table ---
    conn.execute("CREATE SEQUENCE IF NOT EXISTS seq_items_id START 1;")

    # 创建发票信息表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
            id integer PRIMARY KEY DEFAULT nextval('seq_invoices_id') ,
            version_id VARCHAR NOT NULL,
            file_name VARCHAR NOT NULL,
            file_path VARCHAR NOT NULL,
            
            -- 发票基本信息
            invoice_code VARCHAR,           -- 发票代码
            invoice_num VARCHAR,            -- 发票号码
            invoice_date VARCHAR,           -- 开票日期
            invoice_type VARCHAR,           -- 发票类型
            
            -- 购销方信息
            purchaser_name VARCHAR,         -- 购买方名称
            purchaser_register_num VARCHAR, -- 购买方税号
            seller_name VARCHAR,            -- 销售方名称
            seller_register_num VARCHAR,    -- 销售方税号
            
            -- 金额信息
            total_amount DECIMAL(12,2),     -- 合计金额
            total_tax DECIMAL(12,2),        -- 合计税额
            amount_in_figuers DECIMAL(12,2),-- 价税合计(小写)
            amount_in_words VARCHAR,        -- 价税合计(大写)
            
            -- 其他信息
            remarks VARCHAR,                -- 备注
            
            -- 原始数据
            json_data JSON,                 -- 完整JSON数据
            
            scan_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (version_id) REFERENCES versions(version_id)
        )
    ''')

    # 创建商品明细表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS invoice_items (
            id integer PRIMARY KEY DEFAULT nextval('seq_items_id') ,
            invoice_id INTEGER NOT NULL,
            row_num INTEGER,
            
            -- 商品信息
            name VARCHAR,                   -- 商品名称
            specification VARCHAR,          -- 规格型号
            unit VARCHAR,                   -- 单位
            quantity DECIMAL(12,2),         -- 数量
            price DECIMAL(31,13),            -- 单价
            amount DECIMAL(12,2),           -- 金额
            tax_rate VARCHAR,               -- 税率
            tax DECIMAL(12,2),              -- 税额
            
            FOREIGN KEY (invoice_id) REFERENCES invoices(id)
        )
    ''')

    return conn

def create_version(conn, version_name, description=None):
    """创建新版本"""
    version_id = str(uuid.uuid4())
    conn.execute('''
        INSERT INTO versions (version_id, version_name, description)
        VALUES (?, ?, ?)
    ''', [version_id, version_name, description])
    return version_id

def extract_invoice_data(ocr_result, file_name, file_path):
    """从OCR结果中提取发票数据"""
    words = ocr_result.get("words_result", {})
    
    # 提取基本信息
    invoice_data = {
        "file_name": file_name,
        "file_path": file_path,
        "invoice_code": words.get("InvoiceCode", ""),
        "invoice_num": words.get("InvoiceNum", ""),
        "invoice_date": words.get("InvoiceDate", ""),
        "invoice_type": words.get("InvoiceType", ""),
        "purchaser_name": words.get("PurchaserName", ""),
        "purchaser_register_num": words.get("PurchaserRegisterNum", ""),
        "seller_name": words.get("SellerName", ""),
        "seller_register_num": words.get("SellerRegisterNum", ""),
        "total_amount": words.get("TotalAmount", "0"),
        "total_tax": words.get("TotalTax", "0"),
        "amount_in_figuers": words.get("AmountInFiguers", "0"),
        "amount_in_words": words.get("AmountInWords", ""),
        "remarks": words.get("Remarks", ""),
        "json_data": json.dumps(ocr_result, ensure_ascii=False)
    }
    
    # 提取商品明细
    items = []
    commodity_names = words.get("CommodityName", [])
    commodity_types = words.get("CommodityType", [])
    commodity_units = words.get("CommodityUnit", [])
    commodity_nums = words.get("CommodityNum", [])
    commodity_prices = words.get("CommodityPrice", [])
    commodity_amounts = words.get("CommodityAmount", [])
    commodity_tax_rates = words.get("CommodityTaxRate", [])
    commodity_taxes = words.get("CommodityTax", [])
    
    # 获取商品数量
    item_count = len(commodity_names)
    
    for i in range(item_count):
        raw_price=commodity_prices[i]["word"] if i < len(commodity_prices) else "0"
        normalized_price = normalize_price(raw_price)
        item = {
            "row_num": i + 1,
            "name": commodity_names[i]["word"] if i < len(commodity_names) else "",
            "specification": commodity_types[i]["word"] if i < len(commodity_types) else "",
            "unit": commodity_units[i]["word"] if i < len(commodity_units) else "",
            "quantity": commodity_nums[i]["word"] if i < len(commodity_nums) else "0",
            "price": normalized_price,
            "amount": commodity_amounts[i]["word"] if i < len(commodity_amounts) else "0",
            "tax_rate": commodity_tax_rates[i]["word"] if i < len(commodity_tax_rates) else "",
            "tax": commodity_taxes[i]["word"] if i < len(commodity_taxes) else "0"
        }
                # 记录价格修正信息
        if normalized_price != raw_price:
            print(f"  商品 {i+1}: 价格已修正 {raw_price} -> {normalized_price}")

        items.append(item)
    
    return invoice_data, items

def save_to_duckdb(conn, version_id, invoice_data, items):
    """保存发票数据到DuckDB"""
    # 插入发票主表
    conn.execute('''
        INSERT INTO invoices (
            version_id, file_name, file_path, invoice_code, invoice_num, 
            invoice_date, invoice_type, purchaser_name, purchaser_register_num, 
            seller_name, seller_register_num, total_amount, total_tax, 
            amount_in_figuers, amount_in_words, remarks, json_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', [
        version_id, invoice_data["file_name"], invoice_data["file_path"],
        invoice_data["invoice_code"], invoice_data["invoice_num"],
        invoice_data["invoice_date"], invoice_data["invoice_type"],
        invoice_data["purchaser_name"], invoice_data["purchaser_register_num"],
        invoice_data["seller_name"], invoice_data["seller_register_num"],
        invoice_data["total_amount"], invoice_data["total_tax"],
        invoice_data["amount_in_figuers"], invoice_data["amount_in_words"],
        invoice_data["remarks"], invoice_data["json_data"]
    ])
    
    # 获取插入的发票ID
    #invoice_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]
    invoice_id= conn.execute("SELECT currval('seq_invoices_id')").fetchone()[0]
    
    # 插入商品明细
    for item in items:
        conn.execute('''
            INSERT INTO invoice_items (
                invoice_id, row_num, name, specification, unit, 
                quantity, price, amount, tax_rate, tax
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
            invoice_id, item["row_num"], item["name"], item["specification"],
            item["unit"], item["quantity"], item["price"], item["amount"],
            item["tax_rate"], item["tax"]
        ])
    
    conn.commit()
    return invoice_id

def process_image(file_path):
    """处理图片并进行OCR识别"""
    request_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice"
    with open(file_path, 'rb') as f:
        img = base64.b64encode(f.read())
    
    params = {"image": img}
    access_token = '24.ee5f56e8efcc69b3f5d1ee3e9338b6c7.2592000.1755054509.282335-23777987'
    request_url = request_url + "?access_token=" + access_token
    headers = {'content-type': 'application/x-www-form-urlencoded'}
    response = requests.post(request_url, data=params, headers=headers)
    
    if response:
        result = response.json()
        # 保存为同名JSON文件
        json_path = os.path.splitext(file_path)[0] + '.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return result
    return None

def scan_directory(directory, version_name=None, save_to_db=True, db_path='invoice_data.db'):
    """
    扫描目录进行OCR识别并保存到DuckDB
    
    Args:
        directory: 要扫描的目录路径
        version_name: 版本名称，如果为None则使用时间戳
        save_to_db: 是否保存到数据库
        db_path: 数据库文件路径
    """
    all_results = []
    
    # 如果没有指定版本名称，使用时间戳
    if version_name is None:
        version_name = f"扫描_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 初始化数据库连接
    conn = init_database(db_path) if save_to_db else None
    
    # 创建新版本
    version_id = create_version(conn, version_name, f"扫描目录: {directory}") if save_to_db else None
    
    # 扫描目录
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                file_path = os.path.join(root, file)
                print(f"处理文件: {file_path}")
                
                # OCR识别
                ocr_result = process_image(file_path)
                
                if ocr_result:
                    # 提取发票数据
                    invoice_data, items = extract_invoice_data(ocr_result, file, file_path)
                    
                    # 保存到数据库
                    if save_to_db:
                        invoice_id = save_to_duckdb(conn, version_id, invoice_data, items)
                        print(f"  已保存到数据库，发票ID: {invoice_id}")
                    
                    all_results.append({
                        "file_name": file,
                        "file_path": file_path,
                        "invoice_data": invoice_data,
                        "items": items
                    })
    
    # 关闭数据库连接
    if conn:
        conn.close()
    
    print(f"扫描完成，共处理 {len(all_results)} 个文件")
    return all_results

def get_versions(db_path='invoice_data.db'):
    """获取所有版本"""
    conn = duckdb.connect(db_path)
    versions = conn.execute('SELECT * FROM versions ORDER BY created_at DESC').fetchall()
    conn.close()
    return versions

def get_invoices_by_version(version_id, db_path='invoice_data.db'):
    """获取指定版本的所有发票"""
    conn = duckdb.connect(db_path)
    invoices = conn.execute('''
        SELECT * FROM invoices WHERE version_id = ? ORDER BY scan_time
    ''', [version_id]).fetchall()
    conn.close()
    return invoices

def get_invoice_items(invoice_id, db_path='invoice_data.db'):
    """获取发票的商品明细"""
    conn = duckdb.connect(db_path)
    items = conn.execute('''
        SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY row_num
    ''', [invoice_id]).fetchall()
    conn.close()
    return items

def export_version_to_json(version_id, output_path, db_path='invoice_data.db'):
    """导出指定版本的数据到JSON文件"""
    conn = duckdb.connect(db_path)
    
    # 获取版本信息
    version = conn.execute('SELECT * FROM versions WHERE version_id = ?', [version_id]).fetchone()
    
    # 获取发票信息
    invoices = conn.execute('''
        SELECT * FROM invoices WHERE version_id = ? ORDER BY scan_time
    ''', [version_id]).fetchall()
    
    export_data = []
    for invoice in invoices:
        invoice_id = invoice[0]
        
        # 获取商品明细
        items = conn.execute('''
            SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY row_num
        ''', [invoice_id]).fetchall()
        
        # 构建发票数据
        invoice_data = {
            "id": invoice_id,
            "file_name": invoice[2],
            "file_path": invoice[3],
            "invoice_code": invoice[4],
            "invoice_num": invoice[5],
            "invoice_date": invoice[6],
            "invoice_type": invoice[7],
            "purchaser_name": invoice[8],
            "purchaser_register_num": invoice[9],
            "seller_name": invoice[10],
            "seller_register_num": invoice[11],
            "total_amount": float(invoice[12]),
            "total_tax": float(invoice[13]),
            "amount_in_figuers": float(invoice[14]),
            "amount_in_words": invoice[15],
            "remarks": invoice[16],
            "scan_time": invoice[18],
            "items": [
                {
                    "row_num": item[2],
                    "name": item[3],
                    "specification": item[4],
                    "unit": item[5],
                    "quantity": float(item[6]),
                    "price": float(item[7]),
                    "amount": float(item[8]),
                    "tax_rate": item[9],
                    "tax": float(item[10])
                }
                for item in items
            ],
            "json_data": json.loads(invoice[17])
        }
        
        export_data.append(invoice_data)
    
    conn.close()
    
    # 导出到JSON文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump({
            "version_id": version_id,
            "version_name": version[1] if version else "",
            "created_at": version[2] if version else "",
            "description": version[3] if version else "",
            "export_time": datetime.datetime.now().isoformat(),
            "total_invoices": len(export_data),
            "invoices": export_data
        }, f, ensure_ascii=False, indent=2)
    
    print(f"已导出版本 {version_id} 的数据到 {output_path}")

def delete_version(version_id, db_path='invoice_data.db'):
    """删除指定版本的数据"""
    conn = duckdb.connect(db_path)
    
    # 获取该版本下的所有发票ID
    invoice_ids = conn.execute('''
        SELECT id FROM invoices WHERE version_id = ?
    ''', [version_id]).fetchall()
    
    # 删除商品明细
    for invoice_id in invoice_ids:
        conn.execute('DELETE FROM invoice_items WHERE invoice_id = ?', [invoice_id[0]])
    
    # 删除发票记录
    conn.execute('DELETE FROM invoices WHERE version_id = ?', [version_id])
    
    # 删除版本记录
    conn.execute('DELETE FROM versions WHERE version_id = ?', [version_id])
    
    conn.commit()
    conn.close()
    
    print(f"已删除版本 {version_id}")

def normalize_price(price_str):
    """
    规范化价格字符串，处理OCR可能漏掉小数点的情况
    
    规则：
    1. 如果数字超过13位且没有小数点，在从右往左数第13位后插入小数点
    2. 如果数字长度为6-13位且没有小数点，可能也是漏掉了小数点，在倒数第2位插入
    3. 如果是合理的价格，保持不变
    """
    if not price_str or not price_str.strip():
        return "0"
    
    # 移除所有非数字和小数点字符
    price_str = ''.join(c for c in price_str if c.isdigit() or c == '.')
    
    # 如果已经有小数点，直接返回
    if '.' in price_str:
        return price_str
    
    # 如果数字超过13位且没有小数点，在从右往左数第13位后插入小数点
    if len(price_str) > 13:
        insert_pos = len(price_str) - 13
        return price_str[:insert_pos] + '.' + price_str[insert_pos:]
    
    # 如果数字长度为6-13位且没有小数点，可能也是漏掉了小数点，在倒数第2位插入
    if len(price_str) >= 6:
        return price_str[:-2] + '.' + price_str[-2:]
    
    return price_str

# 使用示例
if __name__ == "__main__":
    # 示例1: 处理单个JSON文件并保存到数据库
    with open('demo.json', 'r', encoding='utf-8') as f:
        ocr_result = json.load(f)
    
    # 初始化数据库
    conn = init_database()
    
    # 创建版本
    version_id = create_version(conn, "测试版本", "从demo.json导入")
    
    # 提取发票数据
    invoice_data, items = extract_invoice_data(ocr_result, "demo.jpg", "./demo.jpg")
    
    # 保存到数据库
    invoice_id = save_to_duckdb(conn, version_id, invoice_data, items)
    
    print(f"已保存发票到数据库，ID: {invoice_id}")
    
    # 关闭数据库连接
    conn.close()
    
    # 示例2: 扫描目录
    # scan_directory("./invoice_images", "批量扫描测试")
    scan_directory(r"\\192.168.1.100\部门资料库\设计部\设计部资料共享\新建文件夹\非核验发票","非核验发票")
    # 示例3: 导出版本数据
    # export_version_to_json(version_id, "exported_invoices.json")
