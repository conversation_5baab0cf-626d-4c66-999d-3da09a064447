#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OCR工具演示脚本
展示如何使用增强的百度OCR工具，包括DuckDB存储功能
"""

from baidu_ocr2 import (
    scan_directory, 
    list_versions, 
    export_version_to_json, 
    delete_version,
    init_database,
    get_results_by_version
)
import os

def demo_basic_scan():
    """演示基本扫描功能"""
    print("=== 基本扫描演示 ===")
    
    # 创建测试目录（如果不存在）
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"请将测试图片放入 {test_dir} 目录中")
        return
    
    # 扫描目录并保存到数据库
    results = scan_directory(test_dir, version_name="测试扫描_v1")
    print(f"扫描完成，处理了 {len(results)} 个文件")

def demo_version_management():
    """演示版本管理功能"""
    print("\n=== 版本管理演示 ===")
    
    # 列出所有版本
    versions = list_versions()
    
    if versions:
        # 获取第一个版本的详细信息
        first_version = versions[0]
        version_id = first_version[0]
        
        print(f"\n查看版本 {version_id} 的详细信息:")
        conn = init_database()
        results = get_results_by_version(conn, version_id)
        conn.close()
        
        print(f"该版本包含 {len(results)} 条记录")
        for i, result in enumerate(results[:3]):  # 只显示前3条
            print(f"  {i+1}. 文件: {result[2]}")
            print(f"     路径: {result[3]}")
            print(f"     扫描时间: {result[5]}")

def demo_export_import():
    """演示导出功能"""
    print("\n=== 导出功能演示 ===")
    
    versions = list_versions()
    if versions:
        version_id = versions[0][0]
        export_path = f"export_{version_id[:8]}.json"
        
        # 导出版本数据
        export_version_to_json(version_id, export_path)
        print(f"数据已导出到 {export_path}")
        
        # 显示导出文件大小
        if os.path.exists(export_path):
            size = os.path.getsize(export_path)
            print(f"导出文件大小: {size} 字节")

def demo_database_query():
    """演示数据库查询功能"""
    print("\n=== 数据库查询演示 ===")
    
    conn = init_database()
    
    # 查询统计信息
    total_versions = conn.execute("SELECT COUNT(*) FROM versions").fetchone()[0]
    total_results = conn.execute("SELECT COUNT(*) FROM ocr_results").fetchone()[0]
    
    print(f"数据库统计:")
    print(f"  总版本数: {total_versions}")
    print(f"  总OCR记录数: {total_results}")
    
    # 查询最近的扫描
    recent_scans = conn.execute("""
        SELECT v.version_name, v.created_at, COUNT(o.id) as file_count
        FROM versions v
        LEFT JOIN ocr_results o ON v.version_id = o.version_id
        GROUP BY v.version_id, v.version_name, v.created_at
        ORDER BY v.created_at DESC
        LIMIT 5
    """).fetchall()
    
    print(f"\n最近的扫描:")
    for scan in recent_scans:
        print(f"  版本: {scan[0]}")
        print(f"  时间: {scan[1]}")
        print(f"  文件数: {scan[2]}")
        print("  ---")
    
    conn.close()

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "="*50)
        print("OCR工具交互式演示")
        print("="*50)
        print("1. 基本扫描演示")
        print("2. 版本管理演示")
        print("3. 导出功能演示")
        print("4. 数据库查询演示")
        print("5. 列出所有版本")
        print("6. 扫描指定目录")
        print("0. 退出")
        
        choice = input("\n请选择功能 (0-6): ").strip()
        
        if choice == "0":
            print("再见！")
            break
        elif choice == "1":
            demo_basic_scan()
        elif choice == "2":
            demo_version_management()
        elif choice == "3":
            demo_export_import()
        elif choice == "4":
            demo_database_query()
        elif choice == "5":
            list_versions()
        elif choice == "6":
            directory = input("请输入要扫描的目录路径: ").strip()
            if os.path.exists(directory):
                version_name = input("请输入版本名称 (留空使用默认): ").strip()
                if not version_name:
                    version_name = None
                results = scan_directory(directory, version_name=version_name)
                print(f"扫描完成，处理了 {len(results)} 个文件")
            else:
                print("目录不存在！")
        else:
            print("无效选择，请重试。")

if __name__ == "__main__":
    print("OCR工具演示脚本")
    print("确保已安装必要的依赖: pip install duckdb requests")
    
    # 检查数据库是否存在
    if not os.path.exists("ocr_results.db"):
        print("首次运行，正在初始化数据库...")
        init_database()
        print("数据库初始化完成！")
    
    interactive_menu()
