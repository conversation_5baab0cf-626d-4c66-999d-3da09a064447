# 百度OCR工具 - DuckDB增强版

这是一个增强版的百度OCR工具，支持将OCR结果保存到DuckDB数据库中，并提供版本管理功能。

## 新增功能

### 1. DuckDB数据库存储
- 自动将OCR结果保存到DuckDB数据库
- 支持版本管理，每次扫描可以创建新版本
- 提供完整的数据查询和管理功能

### 2. 版本管理
- 每次扫描可以指定版本名称
- 支持查看所有历史版本
- 可以导出指定版本的数据
- 支持删除不需要的版本

### 3. 数据导出
- 支持将数据库中的数据导出为JSON格式
- 保持与原始JSON格式的兼容性

## 安装依赖

```bash
pip install duckdb requests
```

## 使用方法

### 基本使用

```python
from baidu_ocr2 import scan_directory

# 扫描目录并保存到数据库
results = scan_directory(r'C:\your\image\directory', version_name="我的扫描_v1")
```

### 版本管理

```python
from baidu_ocr2 import list_versions, export_version_to_json, delete_version

# 列出所有版本
versions = list_versions()

# 导出指定版本到JSON文件
export_version_to_json("version-id", "output.json")

# 删除指定版本
delete_version("version-id")
```

### 数据库查询

```python
from baidu_ocr2 import init_database, get_results_by_version

# 连接数据库
conn = init_database()

# 获取指定版本的结果
results = get_results_by_version(conn, "version-id")

conn.close()
```

## 数据库结构

### versions表
- `version_id`: 版本唯一标识符 (UUID)
- `version_name`: 版本名称
- `created_at`: 创建时间
- `description`: 版本描述

### ocr_results表
- `id`: 记录ID (自增)
- `version_id`: 关联的版本ID
- `file_name`: 文件名
- `file_path`: 文件完整路径
- `json_path`: JSON结果文件路径
- `scan_time`: 扫描时间
- `ocr_data`: OCR结果数据 (JSON格式)

## 交互式演示

运行演示脚本来体验所有功能：

```bash
python ocr_demo.py
```

演示脚本提供以下功能：
1. 基本扫描演示
2. 版本管理演示
3. 导出功能演示
4. 数据库查询演示
5. 列出所有版本
6. 扫描指定目录

## API参考

### scan_directory()
```python
scan_directory(directory, version_name=None, save_to_db=True, db_path='ocr_results.db')
```
- `directory`: 要扫描的目录路径
- `version_name`: 版本名称，如果为None则使用时间戳
- `save_to_db`: 是否保存到数据库 (默认: True)
- `db_path`: 数据库文件路径 (默认: 'ocr_results.db')

### list_versions()
```python
list_versions(db_path='ocr_results.db')
```
列出所有版本信息

### export_version_to_json()
```python
export_version_to_json(version_id, output_path, db_path='ocr_results.db')
```
导出指定版本的数据到JSON文件

### delete_version()
```python
delete_version(version_id, db_path='ocr_results.db')
```
删除指定版本的所有数据

## 使用示例

### 示例1: 批量处理多个目录
```python
directories = [
    r'C:\invoices\2024\01',
    r'C:\invoices\2024\02',
    r'C:\invoices\2024\03'
]

for i, directory in enumerate(directories, 1):
    if os.path.exists(directory):
        version_name = f"2024年发票扫描_第{i}批"
        results = scan_directory(directory, version_name=version_name)
        print(f"处理完成: {directory}, 文件数: {len(results)}")
```

### 示例2: 定期备份数据
```python
import datetime

# 获取所有版本
versions = list_versions()

# 导出最近的版本
for version in versions[:5]:  # 导出最近5个版本
    version_id = version[0]
    version_name = version[1]
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"backup_{version_name}_{timestamp}.json"
    export_version_to_json(version_id, output_file)
```

### 示例3: 数据分析
```python
conn = init_database()

# 统计每个版本的文件数量
stats = conn.execute("""
    SELECT v.version_name, COUNT(o.id) as file_count, 
           MIN(o.scan_time) as first_scan, MAX(o.scan_time) as last_scan
    FROM versions v
    LEFT JOIN ocr_results o ON v.version_id = o.version_id
    GROUP BY v.version_id, v.version_name
    ORDER BY file_count DESC
""").fetchall()

for stat in stats:
    print(f"版本: {stat[0]}, 文件数: {stat[1]}, 扫描时间: {stat[2]} - {stat[3]}")

conn.close()
```

## 注意事项

1. 确保百度OCR的access_token是有效的
2. 数据库文件会自动创建，无需手动创建
3. 建议定期备份数据库文件
4. 大量数据处理时注意磁盘空间
5. 版本删除操作不可恢复，请谨慎使用

## 故障排除

### 常见问题

1. **DuckDB导入错误**
   ```bash
   pip install duckdb
   ```

2. **数据库文件权限问题**
   确保程序对数据库文件所在目录有读写权限

3. **OCR API调用失败**
   检查网络连接和access_token是否有效

4. **内存不足**
   处理大量文件时，可以分批处理或增加系统内存
